# -*- mode: ruby -*-
# vi: set ft=ruby :

# Vagrantfile API/syntax version. Don't touch unless you know what you're doing!
VAGRANTFILE_API_VERSION = "2"

Vagrant.configure(VAGRANTFILE_API_VERSION) do |config|
  config.vm.box = "bento/ubuntu-20.04"
  config.ssh.forward_agent = false
  config.vm.define "rewards.local", primary: true do |app|
    app.vm.hostname = "rewards"
    app.vm.network "private_network", ip: "************"
  end

  config.vm.provider "virtualbox" do |vb|
    vb.customize ["modifyvm", :id, "--name", "rewards", "--memory", "1024"]
  end

  # Forward port 80 for the static website (bigbaboon.local)
  config.vm.network "forwarded_port", guest: 80, host: 80

  # Forward port 8080 for API access from local network devices
  # This allows external devices to access the API via http://your-host-ip:8080
  config.vm.network "forwarded_port", guest: 8080, host: 8080

  config.vm.network "forwarded_port", guest: 443, host: 443
  config.vm.network "forwarded_port", guest: 5432, host: 54320

  # For local development, uncommenting and editing the line below will enable
  # a folder in the host machine containing your local git repo to be synced to
  # the guest machine. Ensure the Ansible playbook variable "setup_git_repo" is
  # set to "no" (in env_vars/vagrant.yml) when enabling this.
  config.vm.synced_folder ".", "/var/www/vhosts/rewards.local/rewards"

  # Ansible provisioner.
  config.vm.provision "ansible" do |ansible|
    ansible.compatibility_mode = "2.0"
    ansible.playbook = "ansible/vagrant.yml"
    ansible.host_key_checking = false
    ansible.verbose = "vvvv"
  end
end
