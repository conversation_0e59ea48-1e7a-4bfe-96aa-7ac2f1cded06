---

- name: Provision a web server
  hosts: webservers
  # become: true
  become_user: root
  remote_user: "{{ server_user }}"
  vars:
    update_apt_cache: true
    nging_config_name: web
  vars_files:
    - env_vars/base.yml
    - env_vars/{{ env }}.yml
  module_defaults:
    apt:
      force_apt_get: true

  roles:
    - base
    - certbot
    # - rabbitmq
    - web
    # - celery
    - memcached
    - nginx
