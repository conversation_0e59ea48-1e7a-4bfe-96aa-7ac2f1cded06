---

- name: Add the RabbitMQ release signing key to the apt trusted keys
  apt_key: url=https://packagecloud.io/rabbitmq/rabbitmq-server/gpgkey
           state=present

- name: Add the RabbitMQ repository to the apt sources list
  apt_repository: repo='deb https://packagecloud.io/rabbitmq/rabbitmq-server/ubuntu/ bionic main'
                  update_cache={{ update_apt_cache }}
                  state=present

- name: Install RabbitMQ server
  apt:
    update_cache: "{{ update_apt_cache }}"
    state: present
    name:
      - rabbitmq-server
  tags:
    - packages

- name: Enable the RabbitMQ Management Console
  rabbitmq_plugin: names=rabbitmq_management state=enabled
  notify: restart rabbitmq-server

- name: Make sure rabbitmq-server is enabled and running
  service:
    name: rabbitmq-server
    state: started
    enabled: true

- include: setup_vhosts.yml

- include: setup_users.yml

- name: Ensure that the RabbitMQ service is running
  service: name=rabbitmq-server state=started enabled=yes
