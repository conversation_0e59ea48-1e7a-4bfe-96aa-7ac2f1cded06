---

- name: Ensure the Supervisor service is running
  service: name=supervisor state=started enabled=yes

- name: Create the Supervisor config file for {{ celery_application_name }}
  template: src=supervisor_{{ celery_application_name }}.conf.j2
            dest=/etc/supervisor/conf.d/{{ celery_application_name }}.conf

- name: Create the {{ celery_application_name }} log directory
  file: path={{ celery_log_dir }}
        owner={{ celery_user }}
        group={{ celery_group }}
        state=directory

- name: Check for an existing celery logfile
  stat:
    path: "{{ celery_log_file }}"
  register: p

- name: Create (or retain) the {{ celery_application_name }} log file
  # Removing until https://github.com/ansible/ansible/issues/45530 gets resolved.
  # copy: content=""
  #       dest={{ celery_log_file }}
  #       owner={{ celery_user }}
  #       group={{ celery_group }}
  #       force=no
  file:
    path: "{{ celery_log_file }}"
    owner: "{{ celery_user }}"
    group: "{{ celery_group }}"
    state: '{{ "file" if  p.stat.exists else "touch" }}'

- name: Re-read the Supervisor config files
  supervisorctl: name={{ celery_application_name }} state=present
