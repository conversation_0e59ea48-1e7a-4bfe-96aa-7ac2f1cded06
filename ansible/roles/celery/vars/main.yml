---

server_root_dir: /var/www/

virtualenv_path: "/var/www/vhosts/local.rewards.com/{{ application_name }}"
project_path: "{{ virtualenv_path }}/{{ project_name }}"

celery_user: "{{ application_name }}"
celery_group: webapps

celery_application_name: celery
celery_scripts_dir: "{{ virtualenv_path }}/scripts/celery"
celery_template_file: "{{ celery_application_name }}_start.j2"

celery_log_dir: "{{ virtualenv_path }}/logs"
celery_log_file: "{{ celery_log_dir }}/{{ celery_application_name }}.log"
