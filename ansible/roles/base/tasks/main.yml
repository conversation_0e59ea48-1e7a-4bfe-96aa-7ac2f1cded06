---

- import_tasks: create_swap_file.yml
  when: create_swap_file
  tags: swap

- name: Install security updates
  apt: 
    default_release: "{{ ansible_distribution_release }}-security"
    update_cache: "{{ update_apt_cache }}"
    upgrade: dist
  tags:
    - packages
    - skip_ansible_lint

- name: Install base packages
  apt:
    update_cache: "{{ update_apt_cache }}"
    state: present
    name:
      - locales
      - build-essential
      - acl
      - ntp
      - htop
      - git
      - "{{ base_python_package }}-pip"
      - "{{ base_python_package }}"
      - "{{ base_python_package }}-distutils"
      - supervisor
  tags:
    - packages
    - packages.security

- name: Upgrade pip
  pip: name=pip state=latest
  tags:
    - packages
    - skip_ansible_lint
