---
- name: Create swap file
  command: dd if=/dev/zero of={{ swap_file_path }} bs=1024 count={{ swap_file_size_kb }}k
           creates="{{ swap_file_path }}"
  tags: swap.file.create

- name: Change swap file permissions
  file: path="{{ swap_file_path }}"
        owner={{ server_user }}
        group={{ server_user }}
        mode=0600
  tags: swap.file.permissions

- name: Check swap file type
  command: file {{ swap_file_path }}
  register: swapfile
  tags: swap.file.mkswap
  changed_when: false

- name: Make swap file
  command: "mkswap {{ swap_file_path }}"
  when: swapfile.stdout.find('swap file') == -1
  tags: swap.file.mkswap

- name: Write swap entry in fstab
  mount: name=none
         src={{ swap_file_path }}
         fstype=swap
         opts=sw
         passno=0
         dump=0
         state=present
  tags: swap.fstab

- name: Mount swap
  command: "swapon {{ swap_file_path }}"
  when: ansible_swaptotal_mb < 1
  tags: swap.file.swapon
