---

- name: Install Nginx
  apt: name=nginx update_cache={{ update_apt_cache }} state=present
  tags: packages

- name: Ensure that a strong Diffie-Hellman group is used
  command: openssl dhparam -out /etc/ssl/certs/dhparams.pem 2048 creates=/etc/ssl/certs/dhparams.pem
  when: nginx_strong_dh_group is defined and nginx_strong_dh_group

- name: Create the Nginx configuration file
  template: src=api.j2
            dest=/etc/nginx/sites-available/api
            backup=yes
  notify: reload nginx

- name: Create the Nginx configuration file
  template: src=web.j2
            dest=/etc/nginx/sites-available/web
            backup=yes
  notify: reload nginx

- name: Ensure that the default site is disabled
  file: path=/etc/nginx/sites-enabled/default state=absent
  notify: reload nginx

- name: Ensure that the API application site is enabled
  file: src=/etc/nginx/sites-available/api
        dest=/etc/nginx/sites-enabled/api
        state=link
  notify: reload nginx

- name: Ensure that the web application site is enabled
  file: src=/etc/nginx/sites-available/web
        dest=/etc/nginx/sites-enabled/web
        state=link
  notify: reload nginx

- name: Ensure Nginx service is started
  service: name=nginx state=started enabled=yes
