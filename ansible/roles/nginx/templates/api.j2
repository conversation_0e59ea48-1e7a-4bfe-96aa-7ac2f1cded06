upstream {{ application_name }}_wsgi_server {
  # fail_timeout=0 means we always retry an upstream even if it failed
  # to return a good HTTP response (in case the Unicorn master nukes a
  # single worker for timing out).

  server unix:{{ virtualenv_path }}/run/gunicorn.sock fail_timeout=0;
}

# API subdomain server block
server {
    {% if env == 'local' %}
    listen      8080;
    {% else %}
    listen      80;
    {% endif %}
    server_name {{ nginx_server_name }};

{% if ssl_on is defined and ssl_on %}
    rewrite     ^ https://$server_name$request_uri? permanent;
}

{% if env != 'local' && ssl_on is defined and ssl_on %}
server {
    listen              443 ssl;
    server_name         {{ nginx_server_name }};
    server_tokens       off;
    ssl_certificate     {{ letsencrypt_dir }}/api.bigbaboon.co.uk/{{ letsencrypt_cert_filename }};
    ssl_certificate_key {{ letsencrypt_dir }}/api.bigbaboon.co.uk/{{ letsencrypt_privkey_filename }};
    ssl_protocols       TLSv1.2;
    ssl_ciphers         'ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-DSS-AES128-GCM-SHA256:kEDH+AESGCM:ECDHE-RSA-AES128-SHA256:ECDHE-ECDSA-AES128-SHA256:ECDHE-RSA-AES128-SHA:ECDHE-ECDSA-AES128-SHA:ECDHE-RSA-AES256-SHA384:ECDHE-ECDSA-AES256-SHA384:ECDHE-RSA-AES256-SHA:ECDHE-ECDSA-AES256-SHA:DHE-RSA-AES128-SHA256:DHE-RSA-AES128-SHA:DHE-DSS-AES128-SHA256:DHE-RSA-AES256-SHA256:DHE-DSS-AES256-SHA:DHE-RSA-AES256-SHA:AES128-GCM-SHA256:AES256-GCM-SHA384:AES128-SHA256:AES256-SHA256:AES128-SHA:AES256-SHA:AES:CAMELLIA:DES-CBC3-SHA:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!aECDH:!EDH-DSS-DES-CBC3-SHA:!EDH-RSA-DES-CBC3-SHA:!KRB5-DES-CBC3-SHA';
    ssl_prefer_server_ciphers on;
    {% if nginx_strong_dh_group %}
    ssl_dhparam          /etc/ssl/certs/dhparams.pem;
    {% endif %}

    # Prevent MIME type sniffing for security
    add_header X-Content-Type-Options "nosniff";

    # Enable XSS Protection in case user's browser has disabled it
    add_header X-XSS-Protection "1; mode=block";

    client_max_body_size 4G;

    access_log {{ nginx_access_log_file }};
    error_log {{ nginx_error_log_file }};

    location /static/ {
        alias   {{ nginx_static_dir }};
    }

    location /media/ {
        alias   {{ nginx_media_dir }};
    }

    location / {
        if (-f {{ virtualenv_path }}/maintenance_on.html) {
            return 503;
        }

        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_redirect off;

        # Try to serve static files from nginx, no point in making an
        # *application* server like Unicorn/Rainbows! serve static files.
        if (!-f $request_filename) {
            proxy_pass http://{{ application_name }}_wsgi_server;
            break;
        }
    }

    # Error pages
    error_page 500 502 504 /500.html;
    location = /500.html {
        root {{ project_path }}/{{ application_name }}/templates/;
    }

    error_page 503 /maintenance_on.html;
    location = /maintenance_on.html {
        root {{ virtualenv_path }}/;
    }
}
{% endif %}