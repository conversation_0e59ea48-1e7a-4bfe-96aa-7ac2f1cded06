# Main domain server block
server {
    listen      80;
    server_name {{ server_name }};

{% if ssl_on is defined and ssl_on %}
    rewrite     ^ https://$server_name$request_uri? permanent;
}

server {
    listen              443 ssl;
    server_name         {{ server_name }};
    server_tokens       off;
    {% if nginx_use_letsencrypt %}
    ssl_certificate     {{ letsencrypt_dir }}/bigbaboon.co.uk/{{ letsencrypt_cert_filename }};
    ssl_certificate_key {{ letsencrypt_dir }}/bigbaboon.co.uk/{{ letsencrypt_privkey_filename }};
    {% else %}
    ssl_certificate     {{ nginx_ssl_dest_dir }}/web.{{ application_name }}.crt;
    ssl_certificate_key {{ nginx_ssl_dest_dir }}/web.{{ application_name }}.key;
    {% endif %}
    ssl_protocols       TLSv1.2;
    ssl_ciphers         'ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-DSS-AES128-GCM-SHA256:kEDH+AESGCM:ECDHE-RSA-AES128-SHA256:ECDHE-ECDSA-AES128-SHA256:ECDHE-RSA-AES128-SHA:ECDHE-ECDSA-AES128-SHA:ECDHE-RSA-AES256-SHA384:ECDHE-ECDSA-AES256-SHA384:ECDHE-RSA-AES256-SHA:ECDHE-ECDSA-AES256-SHA:DHE-RSA-AES128-SHA256:DHE-RSA-AES128-SHA:DHE-DSS-AES128-SHA256:DHE-RSA-AES256-SHA256:DHE-DSS-AES256-SHA:DHE-RSA-AES256-SHA:AES128-GCM-SHA256:AES256-GCM-SHA384:AES128-SHA256:AES256-SHA256:AES128-SHA:AES256-SHA:AES:CAMELLIA:DES-CBC3-SHA:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!aECDH:!EDH-DSS-DES-CBC3-SHA:!EDH-RSA-DES-CBC3-SHA:!KRB5-DES-CBC3-SHA';
    ssl_prefer_server_ciphers on;
    {% if nginx_strong_dh_group %}
    ssl_dhparam          /etc/ssl/certs/dhparams.pem;
    {% endif %}

    location /.well-known/ {
        alias {{ project_path }}/.well-known/;
        default_type application/json;
        add_header 'Access-Control-Allow-Origin' '*';
    }

{% endif %}

    location / {
        root {{ project_path }}/bigbaboon/;
        try_files $uri $uri/ =404;
        index index.html;
    }

    location /static/ {
        alias {{ nginx_static_dir }};
        expires 30d;
        access_log off;
        add_header Cache-Control "public, no-transform";
    }

    location /media/ {
        alias {{ nginx_media_dir }};
        expires 30d;
        access_log off;
        add_header Cache-Control "public, no-transform";
    }

    {% if env == 'local' %}
    # For local development, add these security headers
    add_header X-Content-Type-Options "nosniff";
    add_header X-XSS-Protection "1; mode=block";
    {% endif %}
}