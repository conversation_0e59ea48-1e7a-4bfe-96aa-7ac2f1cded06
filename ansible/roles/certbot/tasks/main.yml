---

- name: Install Certbot
  apt:
    name: 
      - certbot
      - python3-certbot-nginx
    update_cache: "{{ update_apt_cache }}"
    state: present    
  tags: packages

- name: Check if Nginx exists
  stat: path=/etc/init.d/nginx
  register: nginx_status

- name: Ensure Nginx is stopped
  service: name=nginx state=stopped
  when: nginx_status.stat.exists

- name: Install certbot and generate API cert
  command: "{{ certbot_script }} certonly --noninteractive --agree-tos --standalone --email {{ certbot_admin_email }} -d api.bigbaboon.co.uk"
  args:
    creates: "{{ certbot_output_dir }}/api.bigbaboon.co.uk"

- name: Install certbot and generate Web cert
  command: "{{ certbot_script }} certonly --noninteractive --agree-tos --standalone --email {{ certbot_admin_email }} -d bigbaboon.co.uk"
  args:
    creates: "{{ certbot_output_dir }}/bigbaboon.co.uk"

- name: Ensure Nginx is started
  service: name=nginx state=started
  when: nginx_status.stat.exists

- name: Ensure a cron job to auto-renew the cert exists
  cron: name="daily auto renew cert"
        special_time=daily
        job="{{ certbot_script }} renew --standalone --no-self-upgrade --pre-hook \"service nginx stop\" --post-hook \"service nginx start\" --quiet"
        state=present
  when: certbot_auto_renew
