# 2003 - <PERSON> <<EMAIL>>
# This configuration file is read by the start-memcached script provided as
# part of the Debian GNU/Linux distribution. 

# Run memcached as a daemon. This command is implied, and is not needed for the
# daemon to run. See the README.Debian that comes with this package for more
# information.
-d

# Log memcached's output to /var/log/memcached
logfile /var/log/memcached.log

# Be verbose
# -v

# Be even more verbose (print client commands as well)
# -vv

# Start with a cap of 64 megs of memory. It's reasonable, and the daemon default
# Note that the daemon will grow to this size, but does not start out holding this much
# memory
-m {{ memcached_max_memory_mb }}

# Default connection port is 11211
-p {{ memcached_port }}

# Run the daemon as root. The start-memcached will default to running as root if no
# -u command is present in this config file
-u {{ memcached_user }}

# Specify which IP address to listen on. The default is to listen on all IP addresses
# This parameter is one of the only security measures that memcached has, so make sure
# it's listening on a firewalled interface.
-l {{ memcached_listen }}

# Limit the number of simultaneous incoming connections. The daemon default is 1024
-c {{ memcached_max_connections }}

# Lock down all paged memory. Consult with the README and homepage before you do this
# -k

# Return error when memory is exhausted (rather than removing items)
# -M

# Maximize core file limit
# -r