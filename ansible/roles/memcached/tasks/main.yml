---

- name: Install Memcached
  apt: name=memcached update_cache={{ update_apt_cache }} state=present
  tags: packages

- name: Create the Memcached configuration file
  template: src=memcached.conf.j2
            dest=/etc/memcached.conf
            mode=0644
            backup=yes
  notify:
    - restart memcached

- name: Ensure the Memcached service is running
  service: name=memcached state=started enabled=yes
