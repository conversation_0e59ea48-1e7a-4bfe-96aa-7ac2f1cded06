---

- name: Generate UK locale
  locale_gen: name=en_GB.UTF-8 state=present

- name: Install PostgreSQL
  apt:
    update_cache: "{{ update_apt_cache }}"
    state: present
    name:
      - postgresql
      - postgresql-contrib
      - "{{ base_python_package }}-psycopg2"
  tags: packages

- name: Ensure the PostgreSQL service is running
  service: name=postgresql state=started enabled=yes

- name: Create database
  become_user: postgres
  postgresql_db: 
    name: "{{ db_name }}"
    encoding: "UTF-8"
    lc_collate: "en_GB.UTF-8"
    lc_ctype: "en_GB.UTF-8"
    template: "template0"
  vars:
    ansible_ssh_pipelining: yes

- name: Create database user
  become_user: postgres
  postgresql_user: 
    db: "{{ db_name }}"
    name: "{{ db_user }}"
    password: "{{ db_password }}"
    encrypted: yes
    priv: ALL
  vars:
    ansible_ssh_pipelining: yes

- name: Ensure user does not have unnecessary privileges
  become_user: postgres
  postgresql_user: 
    name: "{{ db_user }}"
    role_attr_flags: NOSUPERUSER,CREATEDB
    state: present
  vars:
    ansible_ssh_pipelining: yes
