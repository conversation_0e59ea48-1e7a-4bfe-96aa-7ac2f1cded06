---

# Virtualenv settings.
enable_deadsnakes_ppa: true
recreate_virtualenv: false
virtualenv_python_version: "{{ base_python_package }}"


# Application settings.
virtualenv_path: "/var/www/vhosts/{{ nginx_server_name }}"
repo_path: "{{ virtualenv_path }}/{{ project_name }}"
project_path: "{{ repo_path }}/src"
django_path: "{{ project_path }}/core"
application_log_dir: "{{ virtualenv_path }}/logs"
application_log_file: "{{ application_log_dir }}/gunicorn_supervisor.log"
requirements_file: "{{ project_path }}/requirements.txt"


# Gunicorn settings.
gunicorn_user: "{{ application_name }}"
gunicorn_group: webapps


# Nginx settings.
nginx_static_dir: "{{ django_path }}/static/"
nginx_media_dir: "{{ virtualenv_path }}/media/"
nginx_access_log_file: "{{ application_log_dir }}/nginx_access.log"
nginx_error_log_file: "{{ application_log_dir }}/nginx_error.log"


# Django environment variables.
django_environment:
  DJANGO_SETTINGS_MODULE: "{{ django_settings_file }}"
  DJANGO_SECRET_KEY: "{{ django_secret_key }}"
  MEDIA_ROOT: "{{ nginx_media_dir }}"
  STATIC_ROOT: "{{ nginx_static_dir }}"
  DATABASE_NAME: "{{ db_name }}"
  DATABASE_USER: "{{ db_user }}"
  DATABASE_PASSWORD: "{{ db_password }}"
  DATABASE_HOST: "{{ db_host }}"
  DATABASE_PORT: "{{ db_port }}"
  BROKER_URL: "{{ broker_url }}"

# Application user/group settings
application_user: "{{ application_name }}"
application_group: webapps
