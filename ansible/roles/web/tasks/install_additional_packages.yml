---

- name: Add deadsnakes team New Python Versions PPA to the apt sources list
  apt_repository: repo='ppa:deadsnakes/ppa'
                  update_cache={{ update_apt_cache }}
                  state=present
  when: enable_deadsnakes_ppa

- name: Install additional packages
  apt:
    update_cache: "{{ update_apt_cache }}"
    state: present
    name:
      - libcurl4-gnutls-dev
      - gnutls-dev
      - libpq-dev
      - "{{ virtualenv_python_version + '-dev' }}"
      - "{{ virtualenv_python_version }}-distutils"
