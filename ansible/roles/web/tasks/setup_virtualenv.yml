---

- name: Install virtualenv
  pip:
    name: virtualenv
    version: 20.14.1
  tags: packages

- name: Check if Super<PERSON> exists
  stat: path=/etc/init.d/supervisor
  register: supervisor_status
  when: recreate_virtualenv

- name: Ensure all processes managed by <PERSON><PERSON> are stopped if exists
  command: supervisorctl stop all
  when: recreate_virtualenv and supervisor_status.stat.exists

- name: Ensure no existing virtualenv exists
  file:
    state: absent
    path: "{{ virtualenv_path }}/"
  when: recreate_virtualenv

- name: Create the virtualenv
  command: virtualenv -p {{ virtualenv_python_version }} {{ virtualenv_path }}
           creates={{ virtualenv_path }}/bin/activate

- name: Ensure gunicorn and pycurl are installed in the virtualenv
  pip:
    virtualenv: "{{ virtualenv_path }}"
    name:
      - gunicorn
      - pycurl

- name: Create the Gunicorn script file
  template: src=gunicorn_start.j2
            dest={{ virtualenv_path }}/bin/gunicorn_start
            owner={{ gunicorn_user }}
            group={{ gunicorn_group }}
            mode=0755
            backup=yes
  tags: deploy

- name: Create the application log folder
  file: path={{ application_log_dir }}
        owner={{ gunicorn_user }}
        group={{ gunicorn_group }}
        mode=0774
        state=directory

- name: Check for an existing application logfile
  stat:
    path: "{{ application_log_file }}"
  register: p

- name: Create (or retain) the application log file
  # Removing until https://github.com/ansible/ansible/issues/45530 gets resolved.
  # copy: content=""
  #       dest={{ application_log_file }}
  #       owner={{ gunicorn_user }}
  #       group={{ gunicorn_group }}
  #       mode=0664
  #       force=no
  file:
    path: "{{ application_log_file }}"
    owner: "{{ gunicorn_user }}"
    group: "{{ gunicorn_group }}"
    mode: 0664
    state: '{{ "file" if  p.stat.exists else "touch" }}'

- name: Create the virtualenv postactivate script to set environment variables
  template: src=virtualenv_postactivate.j2
            dest={{ virtualenv_path }}/bin/postactivate
            owner={{ gunicorn_user }}
            group={{ gunicorn_group }}
            mode=0644
            backup=yes
  notify: restart application
  tags: deploy

- name: Create the maintenance page
  template: src=maintenance_off.html
            dest={{ virtualenv_path }}/maintenance_off.html
            mode=0664
