---

- name: Install packages required by the Django app inside virtualenv
  pip: virtualenv={{ virtualenv_path }} requirements={{ requirements_file }}

- name: Run Django database migrations
  django_manage:
    command: migrate
    app_path: "{{ project_path }}"
    virtualenv: "{{ virtualenv_path }}"
    settings: "{{ django_settings_file }}"
  environment: "{{ django_environment }}"
  when: run_django_db_migrations is defined and run_django_db_migrations
  tags: django.migrate

- name: Run Django collectstatic
  django_manage:
    command: collectstatic
    app_path: "{{ project_path }}"
    virtualenv: "{{ virtualenv_path }}"
    settings: "{{ django_settings_file }}"
  environment: "{{ django_environment }}"
  when: run_django_collectstatic is defined and run_django_collectstatic
  notify: restart application
  tags: django.collectstatic
