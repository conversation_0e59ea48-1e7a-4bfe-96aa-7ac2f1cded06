---

- name: Ensure that the application file permissions are set properly
  file: path={{ virtualenv_path }}
        recurse=yes
        owner={{ gunicorn_user }}
        group={{ gunicorn_group }}
        state=directory
  changed_when: false

- name: Fix static/media paths permissions
  file:
      path: "{{ item }}"
      state: directory
      group: "{{ gunicorn_group }}"
      recurse: yes
      mode: "u=rwX,g=rX,o=rX" # X != x, remember
  changed_when: false
  become: true
  become_user: root
  with_items:
    - "{{ nginx_static_dir }}"
    - "{{ nginx_media_dir }}"