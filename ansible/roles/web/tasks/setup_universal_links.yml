---

- name: Create .well-known directory
  file:
    path: "{{ project_path }}/.well-known"
    state: directory
    owner: "{{ application_user }}"
    group: "{{ application_group }}"
    mode: '0755'

- name: Copy apple-app-site-association file
  template:
    src: apple-app-site-association.json.j2
    dest: "{{ project_path }}/.well-known/apple-app-site-association"
    owner: "{{ application_user }}"
    group: "{{ application_group }}"
    mode: '0644'
  notify: restart application 