---

# You can use the following Python script to adjust this value.
# pip install passlib
# python -c "from passlib.hash import sha512_crypt; import getpass; print sha512_crypt.encrypt(getpass.getpass())"
server_user_password: $6$rounds=656000$u14yrHsUjVxtng6s$lJG3je6OqZoQi2E8wSv9bzgb7mlKeF08bbw4a3mbeNcApMVf7GeQ9a86kdSr/eiEscyza36Kxn6OMb9myHHB/0

perform_aptitude_dist_upgrade: true

force_ssh_authentication: true

enable_unattended_upgrades: true

enable_ufw: true

enable_fail2ban: false
