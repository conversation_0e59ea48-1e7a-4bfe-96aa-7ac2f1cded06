---

- name: Add user
  user: name="{{ server_user }}" shell="{{ shell }}" password="{{ server_user_password }}"

- name: Install sudo
  apt: name=sudo update_cache={{ update_apt_cache }} state=present cache_valid_time=86400

- name: Add user to sudoers
  lineinfile: dest=/etc/sudoers
              regexp="{{ server_user }} ALL"
              line="{{ server_user }} ALL=(ALL) ALL"
              state=present

- name: Limit su access to sudo group
  command: dpkg-statoverride --update --add root sudo 4750 /bin/su
  register: limit_su_res
  failed_when: limit_su_res.rc != 0 and ("already exists" not in limit_su_res.stderr)
  changed_when: limit_su_res.rc == 0

- name: Disallow root SSH access
  lineinfile: dest=/etc/ssh/sshd_config
              regexp="^PermitRootLogin"
              line="PermitRootLogin no"
              state=present
  notify: restart ssh

- name: Delete root password
  action: shell passwd -d root
  tags: skip_ansible_lint
  changed_when: false
