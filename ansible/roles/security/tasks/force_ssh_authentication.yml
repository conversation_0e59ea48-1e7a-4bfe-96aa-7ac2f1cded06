---

- name: Add authorized_keys for the user
  authorized_key: user={{ server_user }} key="{{ lookup('file', item) }}"
  with_items:
    - "{{ user_public_keys }}"

- name: Disallow password authentication
  lineinfile: dest=/etc/ssh/sshd_config
              regexp="^PasswordAuthentication"
              line="PasswordAuthentication no"
              state=present
  notify: restart ssh

- name: Allow ssh only for primary user
  lineinfile: dest=/etc/ssh/sshd_config
              regexp="^AllowUsers"
              line="AllowUsers {{ server_user }}"
              state=present
  notify: restart ssh
