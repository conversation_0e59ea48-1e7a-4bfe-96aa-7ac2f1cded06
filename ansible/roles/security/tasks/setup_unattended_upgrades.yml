---

- name: Install Unattended Upgrades
  apt: update_cache={{ update_apt_cache }} state=present pkg=unattended-upgrades

- name: Set up unattended upgrades
  copy: src=apt_periodic dest=/etc/apt/apt.conf.d/10periodic

- name: Automatically remove unused dependencies
  lineinfile: dest=/etc/apt/apt.conf.d/50unattended-upgrades
              regexp="Unattended-Upgrade::Remove-Unused-Dependencies"
              line="Unattended-Upgrade::Remove-Unused-Dependencies \"true\";"
              state=present
              create=yes
