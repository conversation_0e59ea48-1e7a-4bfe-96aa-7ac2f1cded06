---

- name: Create a {{ application_name }} virtual machine via vagrant
  hosts: all
  become: true
  become_user: root
  remote_user: vagrant
  vars:
    setup_git_repo: false
    update_apt_cache: true
    env: local
    ssl_on: false
  vars_files:
    - env_vars/base.yml
    - env_vars/vagrant.yml

  roles:
    - base
    - avahi
    - db
    #- rabbitmq
    - web
    #- celery
    - memcached
    - nginx
