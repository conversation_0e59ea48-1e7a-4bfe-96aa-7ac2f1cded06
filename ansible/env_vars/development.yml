---

# Git settings.
setup_git_repo: false
git_branch: dev


# Database settings.
db_user: "{{ application_name }}_db_admin"
db_name: "{{ application_name }}"
db_password: XAAE*zsjJrF!RtKzX!x87Rdg
db_host: 127.0.0.1
db_port: 5432


# Gunicorn settings. For the number of workers, a good rule to follow is
# 2 x number of CPUs + 1
gunicorn_num_workers: 3

# Setting this to 1 will restart the Gunicorn process each time
# you make a request, basically reloading the code. Very handy
# when developing. Set to 0 for unlimited requests (default).
gunicorn_max_requests: 1


# RabbitMQ settings.
rabbitmq_server_name: "{{ inventory_hostname }}"

rabbitmq_admin_user: admin
rabbitmq_admin_password: password

rabbitmq_application_vhost: "{{ application_name }}"
rabbitmq_application_user: "{{ application_name }}"
rabbitmq_application_password: password


# Celery settings.
celery_num_workers: 2


# Application settings.
django_settings_file: "{{ application_name }}.settings.development"
django_secret_key: "akr2icmg1n8%z^3fe3c+)5d0(t^cy-2_25rrl35a7@!scna^1#"

broker_url: "amqp://{{ rabbitmq_application_user }}:{{ rabbitmq_application_password }}@localhost/{{ rabbitmq_application_vhost }}"

run_django_db_migrations: true
run_django_collectstatic: true


apple_apps:
  - team_id: "UAT6W6PV8T"  # Replace with your first app's Team ID
    bundle_id: "com.bigbaboon.rewards.dev"  # Replace with your first app's bundle ID
  - team_id: "UAT6W6PV8T"  # Replace with your second app's Team ID
    bundle_id: "com.bigbaboon.rewardsclerk.dev"  # Replace with your second app's bundle ID