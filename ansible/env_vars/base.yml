---

# git_repo: https://github.com/dansinclair25/rewards.git
git_repo: **************:big-baboon/rewards.git

# Set this flag to true so you can checkout code from a private git repository
# which is setup with an SSH key.
ssh_forward_agent: true

project_name: rewards
application_name: core

# Note that this PPA doesn't guarantee timely updates in case of security issues.
# Simply remove these two vars below if you prefer to use the official PPA and
# default Python version that came with your Linux distro.
#
# More info here: https://launchpad.net/~fkrull/+archive/ubuntu/deadsnakes
enable_deadsnakes_ppa: false
virtualenv_python_version: python3.10
