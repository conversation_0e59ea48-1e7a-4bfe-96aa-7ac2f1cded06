---
dependency:
  name: galaxy
driver:
  name: docker
lint:
  name: yamllint
platforms:
  - name: instance-focal
    image: ubuntu
    image_version: focal
    privileged: true
  - name: instance-xenial
    image: ubuntu
    image_version: xenial
    privileged: true
provisioner:
  name: ansible
  lint:
    name: ansible-lint
  env:
    ANSIBLE_ROLES_PATH: ../../roles/
scenario:
  name: default
verifier:
  name: testinfra
  lint:
    name: flake8
