from .models import Token
from .serializers import TokenSerializer
from rest_framework import permissions, generics
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth.tokens import default_token_generator
from django.contrib.auth import get_user_model
from django.utils.http import urlsafe_base64_decode
from rest_framework.permissions import AllowAny
from django.core.exceptions import ValidationError
from django.shortcuts import render
from accounts.models import password_reset_token  # Import the custom token generator

User = get_user_model()

class CreateTokenView(generics.CreateAPIView):
    queryset = Token.objects.all()
    serializer_class = TokenSerializer
    permission_classes = (permissions.AllowAny,)

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.validated_data['user']
        token, created = Token.objects.get_or_create(user=user)
        status_code = status.HTTP_201_CREATED if created else status.HTTP_200_OK
        return Response({'token': token.key, 'user': user.id}, status=status_code)
    
