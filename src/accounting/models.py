from django.db import models
from django.conf import settings
from django.db.models import Count
import uuid


# Add a type to our Feature model
class Feature(models.Model):
    class FeatureType(models.TextChoices):
        BOOLEAN = 'BOOLEAN', 'Boolean (On/Off)'
        INTEGER = 'INTEGER', 'Integer (Limit)'
        # You could add more types like STRING if needed

    name = models.CharField(max_length=100, unique=True, help_text="e.g., 'number_active_stores'")
    description = models.TextField()
    feature_type = models.CharField(max_length=10, choices=FeatureType.choices, default=FeatureType.BOOLEAN)

    def __str__(self):
        return self.name

class Plan(models.Model):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    # The magic happens here: we now define the relationship through our new model
    features = models.ManyToManyField(Feature, through='PlanFeature', related_name='plans')
    # Price in pence
    price = models.IntegerField(default=0)

    def __str__(self):
        return self.name

# The "Through" model that stores the value
class PlanFeature(models.Model):
    plan = models.ForeignKey(Plan, on_delete=models.CASCADE)
    feature = models.ForeignKey(Feature, on_delete=models.CASCADE)
    
    # Use a CharField for maximum flexibility. We'll parse it based on feature_type.
    value = models.CharField(max_length=100)

    class Meta:
        # Ensure each feature is only defined once per plan
        unique_together = ('plan', 'feature')

    def __str__(self):
        return f"{self.plan.name} - {self.feature.name}: {self.value}"

class UserSubscription(models.Model):
    """The model which holds details on a user's subscription."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(settings.AUTH_USER_MODEL, related_name='subscription', on_delete=models.CASCADE)
    plan = models.ForeignKey(Plan, on_delete=models.SET_NULL, null=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=False)


class SubscriptionPlanPurchase(models.Model):
    """A model that records when a subscription plan was purchased. (i.e. every time a subscription is charged)"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    plan = models.ForeignKey(Plan, blank=False, related_name='purchase', on_delete=models.PROTECT)
    created_at = models.DateTimeField(auto_now_add=True)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.PROTECT)
