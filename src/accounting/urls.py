from django.urls import path
from .views import RetrieveUpdateSubscriptionView#, ListSubscriptionPlanView, CreateSubscriptionView, RetrieveUpdateSubscriptionView

urlpatterns = [
    # path('subscription-plans', ListSubscriptionPlanView.as_view(), name='list_subscription_plans_view'),
    # path('subscriptions', CreateSubscriptionView.as_view(), name='create-subscription-view'),
    path('subscriptions/me', RetrieveUpdateSubscriptionView.as_view(), name='retrieve-my-subscription-view', kwargs={'pk': 'me'})
    # path('subscriptions/<uuid:pk>', RetrieveUpdateSubscriptionView.as_view(), name='retrieve-update-subscription-view'),
]