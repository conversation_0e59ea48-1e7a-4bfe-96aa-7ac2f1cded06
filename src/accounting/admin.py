from django.contrib import admin
from .models import Feature, Plan, PlanFeature, UserSubscription
from django.contrib.auth.models import Group

admin.site.unregister(Group)

class FeatureAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'description')

admin.site.register(Feature, FeatureAdmin)


class PlanAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'description', 'feature_count')

    @admin.display(description='Number of Features')
    def feature_count(self, obj):
        return obj.features.count()

admin.site.register(Plan, PlanAdmin)


class PlanFeatureAdmin(admin.ModelAdmin):
    list_display = ('id', 'plan', 'feature', 'value')

admin.site.register(PlanFeature, PlanFeatureAdmin)


class UserSubscriptionAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'plan_name', 'expires_at')

    @admin.display(description='Plan')
    def plan_name(self, obj):
        return obj.plan.name if obj.plan else 'None'

admin.site.register(UserSubscription, UserSubscriptionAdmin)
