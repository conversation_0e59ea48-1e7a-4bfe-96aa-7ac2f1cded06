from rest_framework import serializers
from .models import Feature, Plan, UserSubscription

class PlanSerializer(serializers.ModelSerializer):
    features = serializers.SerializerMethodField()

    class Meta:
        model = Plan
        fields = ['id', 'name', 'description', 'features', 'price']

    def get_features(self, obj):
        """
        Builds a dictionary of all features and their parsed values for this plan.
        'obj' is the Plan instance.
        """
        # Using the reverse relation 'planfeature_set' is efficient.
        # .select_related('feature') prevents N+1 database queries.
        plan_features = obj.planfeature_set.all().select_related('feature')

        features = []
        for pf in plan_features:
            value = pf.value
            # Parse the value based on the feature's defined type
            if pf.feature.feature_type == Feature.FeatureType.INTEGER:
                try:
                    value = int(value)
                except (ValueError, TypeError):
                    value = 0
            elif pf.feature.feature_type == Feature.FeatureType.BOOLEAN:
                value = value.lower() in ('true', '1', 'yes')

            features.append({"name": pf.feature.name, "value": value})
        
        return features


class UserSubscriptionSerializer(serializers.ModelSerializer):
    plan = PlanSerializer(read_only=True)

    class Meta:
        model = UserSubscription
        fields = ['id', 'plan', 'is_active', 'expires_at']