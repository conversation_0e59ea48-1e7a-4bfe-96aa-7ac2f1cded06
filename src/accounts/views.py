from .models import User, PrivacyPolicy, PrivacyPolicyVersion, PrivacyPolicyAcceptance, password_reset_token
from .serializers import UserSerializer, VerifyUserSerializer, PrivacyPolicyVersionSerializer, PrivacyPolicyAcceptanceSerializer
from rest_framework import permissions, generics, status
from rest_framework.response import Response
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.db.models import Max, F

from authentication.models import Token
from .services import send_reset_email

class IsUser(permissions.BasePermission):
    def has_object_permission(self, request, view, obj):
        return obj == request.user


class CreateUserView(generics.CreateAPIView):
    queryset = User.objects.all()
    serializer_class = UserSerializer

    def create(self, request, *args, **kwargs):
        # Create User
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        # Create Token
        token = Token.objects.create(user=user)

        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

class RetrieveUpdateUserView(generics.RetrieveUpdateAPIView):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [IsUser]

    def get_object(self):
        if self.kwargs.get('pk', None) == 'me':
            self.kwargs['pk'] = self.request.user.pk

        return super(RetrieveUpdateUserView, self).get_object()
    

class VerifyUserView(generics.RetrieveAPIView):
    queryset = User.objects.all()
    serializer_class = VerifyUserSerializer
    permission_classes = [permissions.IsAuthenticated]


class CreateUserPrivacyPolicyAcceptancesView(generics.CreateAPIView):
    serializer_class = PrivacyPolicyAcceptanceSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        
        user_id = self.kwargs['pk']

        user = User.objects.get(pk=user_id)

        if user != self.request.user:
            raise Exception()
        
        return user.privacy_policies.all()
    
    def post(self, request, *args, **kwargs):
        # Inject additional data into request.data
        request.data['user'] = self.request.user.id
        return super().post(request, *args, **kwargs)


class ListNewPrivacyPolicyVersionsForUser(generics.ListAPIView):
    serializer_class = PrivacyPolicyVersionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user_id = self.kwargs['pk']

        user = User.objects.get(pk=user_id)

        if user != self.request.user:
            raise Exception()
        
        # return a list of the latest versions of each policy that the user has not accepted
        return PrivacyPolicyVersion.objects.exclude(
            id__in=user.privacy_policies.values('version')
        ).annotate(
            max_version=Max('policy__versions__version')
        ).filter(
            version=F('max_version')
        )
    
    
class ResetPasswordView(generics.GenericAPIView):
    permission_classes = []  # Allow unauthenticated access
    
    def post(self, request, *args, **kwargs):
        email = request.data.get('email')
        
        if not email:
            return Response(
                {'error': 'Email is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        # Try to find user with provided email
        try:
            user = User.objects.get(email=email)
            # Send reset email
            token = send_reset_email(user)
            return Response({'token': token}, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            # Return 200 even if user doesn't exist to prevent email enumeration
            return Response({'status': 'SHIT'} , status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Return empty 200 response regardless of whether email exists
        return Response({}, status=status.HTTP_200_OK)


class ConfirmPasswordResetView(generics.GenericAPIView):
    permission_classes = []  # Allow unauthenticated access
    
    def post(self, request, *args, **kwargs):
        token = request.data.get('token')
        new_password = request.data.get('new_password')
        email = request.data.get('email')
        
        if not token or not new_password or not email:
            return Response(
                {'error': 'Token, new password, and email are required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Find user by email
            try:
                user = User.objects.get(email=email)
            except User.DoesNotExist:
                return Response(
                    {'error': 'Invalid or expired token'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Verify token
            if not password_reset_token.check_token(user, token):
                return Response(
                    {'error': 'Invalid or expired token'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Validate new password
            try:
                validate_password(new_password, user)
            except ValidationError as e:
                return Response(
                    {'error': list(e.messages)},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Set new password
            user.set_password(new_password)
            user.save()
            return Response({'status': 'success'}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
