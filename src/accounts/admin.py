from django.contrib import admin
from django.contrib.auth.admin import UserAdmin

from .forms import UserCreationForm, UserChangeForm
from .models import User, PrivacyPolicy, PrivacyPolicyVersion, PrivacyPolicyAcceptance


class UserAdmin(UserAdmin):
    add_form = UserCreationForm
    form = UserChangeForm
    model = User
    list_display = ['email', 'is_clerk', 'id']
    list_filter = ['email', 'is_clerk', 'id']
    fieldsets = (
        (None, {'fields': ('first_name', 'last_name')}),
        (None, {'fields': ('email', 'password')}),
        ('Permissions', {'fields': ('is_clerk', 'is_staff', 'is_superuser', 'is_admin')}),
    )
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('first_name', 'last_name', 'email', 'password1', 'password2', 'is_clerk', 'is_staff', 'is_superuser', 'is_admin')}
        ),
    )
    search_fields = ['email', 'id']
    ordering = ['email', 'id']


admin.site.register(User, UserAdmin)


class PrivacyPolicyAdmin(admin.ModelAdmin):
    list_display = ('id', 'type', 'latest_version', 'created_at')
    ordering = ['created_at', 'type']

    @admin.display(description='Latest Version')
    def latest_version(self, obj):
        try:
            return obj.latest_version.version
        except:
            return 0


admin.site.register(PrivacyPolicy, PrivacyPolicyAdmin)


class PrivacyPolicyVersionAdmin(admin.ModelAdmin):
    list_display = ('id', 'policy_type', 'content', 'version', 'created_at')
    ordering = ['created_at', 'policy__type', 'version']

    @admin.display(ordering='policy__type', description='Policy Type')
    def policy_type(self, obj):
        return obj.policy.type


admin.site.register(PrivacyPolicyVersion, PrivacyPolicyVersionAdmin)