import smtplib
from django.conf import settings
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from .models import password_reset_token

def send_reset_email(user):
    # Email server settings
    smtp_server = "smtp.gmail.com"
    port = 587
    sender_email = settings.EMAIL_HOST_USER
    password = settings.EMAIL_HOST_PASSWORD

    # Generate reset token
    token = password_reset_token.make_token(user)
    
    # Create message
    message = MIMEMultipart("alternative")
    message["Subject"] = "Password Reset Request"
    message["From"] = sender_email
    message["To"] = user.email

    # Create the plain-text and HTML version of your message
    text = """
    Hi,
    You've requested to reset your password. 
    Please click the following link to reset your password:
    {reset_link}
    
    If you didn't request this, you can safely ignore this email.
    """

    html = """
    <html>
      <body>
        <p>Hi,</p>
        <p>You've requested to reset your password.</p>
        <p>Please click the following link to reset your password:</p>
        <p><a href="{reset_link}">Reset Password</a></p>
        <p>If you didn't request this, you can safely ignore this email.</p>
      </body>
    </html>
    """

    # Create reset link with token
    reset_link = f"https://{settings.IOS_APP_DOMAIN}/reset-password?token={token}&email={user.email}"
    text = text.format(reset_link=reset_link)
    html = html.format(reset_link=reset_link)

    part1 = MIMEText(text, "plain")
    part2 = MIMEText(html, "html")

    # Add HTML/plain-text parts to MIMEMultipart message
    message.attach(part1)
    message.attach(part2)

    try:
        # Create secure SSL/TLS connection
        server = smtplib.SMTP(smtp_server, port)
        server.starttls()
        server.login(sender_email, password)
        server.sendmail(sender_email, user.email, message.as_string())
        return token
    except Exception as e:
        print(f"Error sending email: {e}")
        raise e
    finally:
        server.quit()