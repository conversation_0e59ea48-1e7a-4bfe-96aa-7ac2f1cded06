from django.urls import path
from . import views

appname = 'users'

urlpatterns = [
    path('users', views.CreateUserView.as_view(), name='create_user'),
    path('users/me', views.RetrieveUpdateUserView.as_view(), name='me_view', kwargs={'pk': 'me'}),
    path('users/<uuid:pk>', views.RetrieveUpdateUserView.as_view(), name='user_view'),
    path('users/verify/<uuid:pk>', views.VerifyUserView.as_view(), name='verify_user_view'),
    path('users/<uuid:pk>/privacy-policy-versions', views.ListNewPrivacyPolicyVersionsForUser.as_view(), name='list_new_privacy_policy_versions_for_user_view'),
    path('users/<uuid:pk>/privacy-policies', views.CreateUserPrivacyPolicyAcceptancesView.as_view(), name='create_user_privacy_policy_acceptance_view'),
    path('reset-password/', views.ResetPasswordView.as_view(), name='reset-password'),
    path('reset-password/confirm/', views.ConfirmPasswordResetView.as_view(), name='reset-password-confirm'),
]