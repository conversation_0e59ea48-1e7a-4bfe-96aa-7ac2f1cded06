from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin
from django.db import models
from django.db.models.functions import Coalesce

import uuid

from .managers import UserManager
from django.contrib.auth.tokens import PasswordResetTokenGenerator


class User(AbstractBaseUser, PermissionsMixin):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(
        verbose_name='email address',
        max_length=255,
        unique=True,
    )
    first_name = models.CharField(max_length=256, blank=True, null=True)
    last_name = models.CharField(max_length=256, blank=True, null=True)
    is_admin = models.BooleanField(default=False)
    is_staff = models.BooleanField(default=False)
    is_superuser = models.BooleanField(default=False)

    is_clerk = models.BooleanField(default=False)

    def full_name(self):
        all_fields = [self.first_name, self.last_name]
        valid_fields = [f for f in all_fields if f]
        return ' '.join(valid_fields)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    objects = UserManager()

    def __str__(self):
        if self.full_name():
            return self.full_name()
        else:
            return str(self.id)
        


class PrivacyPolicy(models.Model):
    TYPE_CHOICES = (
        ('privacy', 'Privacy Policy'),
        ('terms', 'Terms & Conditions')
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    type = models.CharField(max_length=10, choices=TYPE_CHOICES, unique=True)
    created_at = models.DateTimeField(auto_now_add=True, editable=False)

    class Meta:
        verbose_name_plural = 'privacy policies'

    @property
    def latest_version(self):
        return self.versions.order_by('-version').first()
    
    def __str__(self):
        return self.type


class PrivacyPolicyVersion(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    policy = models.ForeignKey(PrivacyPolicy, on_delete=models.CASCADE, related_name='versions')
    content = models.TextField()
    version = models.IntegerField(editable=False)
    created_at = models.DateTimeField(auto_now_add=True, editable=False)

    class Meta:
        # Ensure that a user can only create a single PrivacyPolicyAcceptance object for each version of each policy type
        unique_together = ('version', 'policy')

    def save(self, *args, **kwargs):
        self.version = self._get_version_number()

        super(PrivacyPolicyVersion, self).save(*args, **kwargs)

    def _get_version_number(self):
        return PrivacyPolicyVersion.objects.filter(policy__type=self.policy.type).count() + 1


class PrivacyPolicyAcceptance(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='privacy_policies')
    version = models.ForeignKey(PrivacyPolicyVersion, on_delete=models.CASCADE)
    accepted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True, editable=False)

    class Meta:
        # Ensure that a user can only create a single PrivacyPolicyAcceptance object for each version of each policy type
        unique_together = ('user', 'version')


class AccountTokenGenerator(PasswordResetTokenGenerator):
    def _make_hash_value(self, user, timestamp):
        return f"{user.pk}{timestamp}{user.password}"

password_reset_token = AccountTokenGenerator()