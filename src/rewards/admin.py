from django.contrib import admin
from .models import Address, Company, Point, Report, Reward, Store
from .utils.monthly_report_generator import generate_monthly_reports

from datetime import datetime
import calendar

# Register your models here.

class AddressAdmin(admin.ModelAdmin):
    list_display = ('id', 'line1', 'line2', 'line3', 'city', 'county', 'postcode', 'latitude', 'longitude')
    search_fields = ['id', 'line1', 'line2', 'line3', 'city', 'county', 'postcode', 'latitude', 'longitude']

admin.site.register(Address, AddressAdmin)


class CompanyAdmin(admin.ModelAdmin):
    list_display = ('image_tag', 'name', 'id', 'store_count', 'reward_count')
    list_display_links = ('image_tag', 'name')  # Make both image and name clickable
    search_fields = ['id', 'name']
    list_filter = ['created_at']
    actions = ['run_company_monthly_reports']
    readonly_fields = ['image_tag', 'store_count', 'reward_count']
    ordering = ['name']

    @admin.display(description='Number of Stores')
    def store_count(self, obj):
        return obj.stores.count()

    @admin.display(description='Number of Rewards')
    def reward_count(self, obj):
        return obj.rewards.count()

    @admin.action(description='Run monthly report for selected Companies')
    def run_company_monthly_reports(modeladmin, request, queryset):
        generate_monthly_reports(queryset, datetime.today())

admin.site.register(Company, CompanyAdmin)


class RewardAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'company_name')
    list_select_related = ('company',)
    search_fields = ['id', 'company__name', 'name']

    @admin.display(ordering='company__name', description='Company')
    def company_name(self, obj):
        return obj.company.name

admin.site.register(Reward, RewardAdmin)


class StoreAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'company_name')
    list_select_related = ('company',)
    search_fields = ['id', 'company__name', 'name']

    @admin.display(ordering='company__name', description='Company')
    def company_name(self, obj):
        return obj.company.name
    
admin.site.register(Store, StoreAdmin)


class PointAdmin(admin.ModelAdmin):
    list_display = ('id', 'reward_name', 'amount', 'user_id', 'store_name', 'created_at')
    list_select_related = ('reward', 'store', 'user')
    search_fields = ['reward__name', 'store__name', 'user__id', 'id']

    @admin.display(ordering='reward__name', description='Reward')
    def reward_name(self, obj):
        return obj.reward.name
    
    @admin.display(ordering='user__id', description='User')
    def user_id(self, obj):
        return obj.user.pk
    
    @admin.display(ordering='store__name', description='Store')
    def store_name(self, obj):
        return obj.store.name
    
admin.site.register(Point, PointAdmin)


class ReportAdmin(admin.ModelAdmin):
    list_display = ('id', 'period', 'store_name', 'company_name')
    list_select_related = ('company', 'store')
    search_fields = ['company__name', 'store__name', 'id']
    ordering = ['start_date', 'store__name']

    @admin.display(ordering='start_date', description='Period')
    def period(self, obj):
        return f'{calendar.month_name[obj.start_date.month]}, {obj.start_date.year}'

    @admin.display(ordering='company__name', description='Company')
    def company_name(self, obj):
        return obj.company.name

    @admin.display(ordering='store__name', description='Store')
    def store_name(self, obj):
        return obj.store.name
    
admin.site.register(Report, ReportAdmin)
