from ..models import Point, Report
from datetime import datetime, timedelta

def start_of_first_day_of_month(date=datetime.today()):
    return date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

def end_of_last_day_of_month(date=datetime.today()):
    next_month = date.replace(day=28) + timedelta(days=4)
    first_day_of_next_month = start_of_first_day_of_month(next_month)
    return first_day_of_next_month - timedelta(seconds=1)

def generate_monthly_reports(queryset, date: datetime):
    start_date = start_of_first_day_of_month(date=date)
    end_date = end_of_last_day_of_month(date=date)

    reports = []
    for company in queryset:
        for store in company.stores.all():
            points = set(Point.objects.filter(store=store, created_at__gte=start_date, created_at__lte=end_date).values_list('user', flat=True))

            try:
                report = Report.objects.filter(start_date=start_date, end_date=end_date, store=store, company=store.company).first()
                
                if report.usage == len(points):
                    continue

                print(f'UPDATING {report.id} from {report.usage} to {len(points)}')
                report.usage = len(points)
                report.save()
            except:
                reports.append(Report(start_date=start_date, end_date=end_date, store=store, company=store.company, usage=len(points)))


    Report.objects.bulk_create(reports)