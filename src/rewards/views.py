from .mixins import ReadWriteSerializerMixin
from .models import Company, Store, Reward, Point
from .permissions import CanCreateReward, CanGetPoint, IsOwnerOrReadOnly, IsUser, IsOwner, IsOwnerOrReadOnly
from .serializers import CompanySerializer, StoreSerializer, RewardSerializer, PointReadSerializer, PointWriteSerializer, RewardPointsSummarySerializer
from rest_framework import generics, permissions, filters, parsers, status
from rest_framework.response import Response
# from rest_framework import #MultiPartParser, FormParser, JSONParser
from django.contrib.auth import get_user_model
from django.db.models import Sum, Max, F
from django.shortcuts import get_object_or_404

User = get_user_model()


## Company
# Anyone can get the list of Companies
# Anyone can create a Company
class ListCreateCompanyView(generics.ListCreateAPIView):
	serializer_class = CompanySerializer
	parser_classes = [parsers.MultiPartParser, parsers.FormParser, parsers.JSONParser]
	permission_classes = [IsOwnerOrReadOnly]
	filter_backends = [filters.OrderingFilter]
	ordering_fields = ['created_at', 'updated_at', 'name', 'id']

	def get_queryset(self):
		queryset = Company.objects.all()

		name = self.request.query_params.get('name')
		if name is not None:
			queryset = queryset.filter(name__icontains=name)

		owner = self.request.query_params.get('owner')
		if owner is not None:
			queryset = queryset.filter(owner=owner)

		ids = self.request.query_params.get('ids')
		if ids is not None:
			queryset = queryset.filter(pk__in=ids.split(','))

		return queryset


class ListUserCompaniesView(generics.ListAPIView):
	serializer_class = CompanySerializer
	permission_classes = [IsUser]

	def get_queryset(self):
		user_id = self.kwargs['pk']
		user = get_object_or_404(User.objects.select_related(), pk=user_id)
		return user.owned_companies.all()


# Anyone can get a Company
class GetUpdateCompanyView(generics.RetrieveUpdateAPIView):
	queryset = Company.objects.all()
	serializer_class = CompanySerializer
	permission_classes = [IsOwnerOrReadOnly]


class ListCompanyRewardsView(generics.ListAPIView):
	serializer_class = RewardSerializer

	def get_queryset(self):
		company_id = self.kwargs['pk']
		company = get_object_or_404(Company.objects.prefetch_related('rewards'), pk=company_id)
		return company.rewards.all()


class ListCreateCompanyStoresView(generics.ListCreateAPIView):
	serializer_class = StoreSerializer
	permission_classes = [IsOwnerOrReadOnly]
	queryset = Company.objects.all()

	def create(self, request, *args, **kwargs):
		company = self.get_object()

		data = request.data
		data['company'] = company.id
		data['owner'] = request.user.id

		serializer = self.get_serializer(data=data)
		serializer.is_valid(raise_exception=True)

		serializer.save()

		headers = self.get_success_headers(serializer.data)
		
		return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)
	
	def list(self, request, *args, **kwargs):
		company = self.get_object()
		stores = company.stores.all().order_by('name')
		serializer = self.get_serializer(stores, many=True)
		return Response(serializer.data)


## Store
class ListStoreView(generics.ListAPIView):
	serializer_class = StoreSerializer
	permission_classes = [permissions.IsAuthenticated]
	filter_backends = [filters.OrderingFilter]
	ordering_fields = ['created_at', 'updated_at', 'name', 'company', 'id']

	def get_queryset(self):
		queryset = Store.objects.all()

		name = self.request.query_params.get('name')
		if name is not None:
			queryset = queryset.filter(name__icontains=name)

		owner = self.request.query_params.get('owner')
		if owner is not None:
			queryset = queryset.filter(owner=owner)

		company = self.request.query_params.get('company')
		if company is not None:
			queryset = queryset.filter(company=company)

		ids = self.request.query_params.get('ids')
		if ids is not None:
			queryset = queryset.filter(pk__in=ids.split(','))

		return queryset


# Anyone can get a Store
class GetUpdateStoreView(generics.RetrieveUpdateDestroyAPIView):
	queryset = Store.objects.all()
	serializer_class = StoreSerializer
	permission_classes = [IsOwnerOrReadOnly]


class ListStoreRewardsView(generics.ListAPIView):
	serializer_class = RewardSerializer

	def get_queryset(self):
		store_id = self.kwargs['pk']
		store = get_object_or_404(Store.objects.select_related(), pk=store_id)
		return store.rewards.all()


## Reward
# Anyone can get the list of Rewards a Store has
# Only the Store owner can create Rewards
class ListCreateRewardsView(generics.ListCreateAPIView):
	serializer_class = RewardSerializer
	permission_classes = [permissions.IsAuthenticated]
	filter_backends = [filters.OrderingFilter]
	filterset_fields = ['owner', 'company', 'store', 'amount']
	ordering_fields = ['created_at', 'updated_at', 'name', 'company', 'store', 'amount']

	def get_queryset(self):
		queryset = Reward.objects.all()

		name = self.request.query_params.get('name')
		if name is not None:
			queryset = queryset.filter(name__icontains=name)

		description = self.request.query_params.get('description')
		if description is not None:
			queryset = queryset.filter(description__icontains=description)

		owner = self.request.query_params.get('owner')
		if owner is not None:
			queryset = queryset.filter(owner=owner)

		company = self.request.query_params.get('company')
		if company is not None:
			queryset = queryset.filter(company=company)

		store = self.request.query_params.get('store')
		if store is not None:
			queryset = queryset.filter(stores__id__exact=store)

		amount = self.request.query_params.get('amount')
		if amount is not None:
			queryset = queryset.filter(amount=amount)

		return queryset


class GetUpdateRewardView(generics.RetrieveUpdateAPIView):
	queryset = Reward.objects.all()
	serializer_class = RewardSerializer
	permission_classes  = [permissions.IsAuthenticated]


class ListRewardStoresView(generics.ListAPIView):
	serializer_class = StoreSerializer

	def get_queryset(self):
		store_id = self.kwargs['pk']
		store = get_object_or_404(Reward.objects.prefetch_related('stores'), pk=store_id)
		return store.stores.all()


class RewardPointsSummaryView(generics.ListAPIView):
	serializer_class = RewardPointsSummarySerializer
	permission_classes  = [permissions.IsAuthenticated]

	def get_queryset(self):
		user = self.request.query_params.get('user', self.request.user)
		store = self.request.query_params.get('store')

		queryset = Reward.objects.filter(points__user=user).order_by('created_at')

		if store:
			queryset = queryset.filter(stores__id=store, company__owner=self.request.user)

		queryset = queryset.annotate(earned=Sum('points__amount'), last_collected_at=Max('points__created_at')).filter(earned__gt=0).order_by('-last_collected_at')

		return queryset
	

class ListStoreRedeemablesView(generics.ListAPIView):
	serializer_class = RewardPointsSummarySerializer
	permission_classes = [IsOwner]
	
	def get_queryset(self):
		store = self.get_object()
		user = self.request.query_params.get('user')

		queryset = store.rewards.all()

		if user:
			queryset = queryset.filter(points__user=user)

		queryset = queryset.annotate(earned=Sum('points__amount'), last_collected_at=Max('points__created_at')).filter(earned__gte=F('amount')).order_by('name')

		return queryset
	
	def get_object(self):
		queryset = Store.objects.all()

        # Perform the lookup filtering.
		lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field

		filter_kwargs = {self.lookup_field: self.kwargs[lookup_url_kwarg]}
		obj = get_object_or_404(queryset, **filter_kwargs)

		# May raise a permission denied
		self.check_object_permissions(self.request, obj)

		return obj


## Point
# Only a Reward owner can create a Point for that reward
# A user can only get their own rewards


class ListUserPointsView(generics.ListAPIView):
	serializer_class = PointReadSerializer
	permission_classes = [IsUser]

	def get_queryset(self):
		user_id = self.kwargs['pk']
		user = get_object_or_404(User.objects.select_related(), pk=user_id)
		return user.points.all()


class ListCreateStorePointsView(generics.ListCreateAPIView):
	serializer_class = PointReadSerializer # Used for `list`
	permission_classes = [IsOwner]
	queryset = Store.objects.all()

	def create(self, request, *args, **kwargs):
		store = self.get_object()

		valid_objects = []
		for object in request.data:
			try:
				if store.rewards.filter(id=object['reward']).exists():
					valid_objects.append(object)
			except:
				raise
				continue

		context = self.get_serializer_context()

		write_serializer = PointWriteSerializer(data=valid_objects, many=True, context=context)
		write_serializer.is_valid(raise_exception=True)
		instance = write_serializer.save(store=store)

		read_serializer = PointReadSerializer(instance, many=True, context=context)
		headers = self.get_success_headers(read_serializer.data)
		return Response(read_serializer.data, status=status.HTTP_201_CREATED, headers=headers)

	def list(self, request, *args, **kwargs):
		store = self.get_object()

		serializer = self.get_serializer(store.points.all(), many=True)
		return Response(serializer.data)