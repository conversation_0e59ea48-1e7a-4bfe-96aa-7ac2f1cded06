from rest_framework import permissions

from .models import Company, Store, Reward, Point

class IsUser(permissions.BasePermission):
	def has_permission(self, request, view):
	  return request.user.id == view.kwargs['pk']
	
	
class IsOwner(permissions.BasePermission):
	def has_object_permission(self, request, view, obj):
		# Instance must have an attribute named `owner`.
		return obj.owner == request.user


class CanCreateReward(permissions.BasePermission):
	def has_permission(self, request, view):
		return request.user.is_authenticated

	def has_object_permission(self, request, view, obj):
		if request.user.is_superuser:
			return True

		if obj.company.owner == request.user:
			return True

		return False


class CanGetPoint(permissions.BasePermission):
	def has_permission(self, request, view):
		return request.user.is_authenticated
  
	def has_object_permission(self, request, view, obj):
		return obj.user == request.user


class IsOwnerOrReadOnly(permissions.BasePermission):
	def has_object_permission(self, request, view, obj):
		# Read permissions are allowed to any request,
		# so we'll always allow GET, HEAD or OPTIONS requests.
		if request.method in permissions.SAFE_METHODS:
			return True

		# Instance must have an attribute named `owner`.
		return IsOwner().has_object_permission(request, view, obj)
