# Generated by Django 3.2 on 2022-08-26 09:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('rewards', '0003_add_created_at_to_Company'),
    ]

    operations = [
        migrations.RenameField(
            model_name='company',
            old_name='users',
            new_name='employees',
        ),
        migrations.AddField(
            model_name='store',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='store',
            name='employees',
            field=models.ManyToManyField(related_name='stores', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='store',
            name='owner',
            field=models.OneToOneField(default=False, on_delete=django.db.models.deletion.CASCADE, related_name='owned_stores', to='accounts.user'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='store',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='owner',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='owned_companies', to=settings.AUTH_USER_MODEL),
        ),
    ]
