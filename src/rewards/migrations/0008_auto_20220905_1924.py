# Generated by Django 3.2 on 2022-09-05 19:24

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('rewards', '0007_auto_20220831_1612'),
    ]

    operations = [
        migrations.AddField(
            model_name='reward',
            name='company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rewards', to='rewards.company'),
            preserve_default=False,
        ),
        migrations.RemoveField(
            model_name='reward',
            name='store',
        ),
        migrations.AddField(
            model_name='reward',
            name='store',
            field=models.ManyToManyField(related_name='rewards', to='rewards.Store'),
        ),
    ]
