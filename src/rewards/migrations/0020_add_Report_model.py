# Generated by Django 3.2 on 2023-09-13 20:12

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import rewards.models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('rewards', '0019_set_order_on_store'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='company',
            options={'verbose_name_plural': 'Companies'},
        ),
        migrations.AlterModelOptions(
            name='point',
            options={'ordering': ['created_at']},
        ),
        migrations.AlterField(
            model_name='reward',
            name='amount',
            field=models.PositiveIntegerField(default=0, validators=[django.core.validators.MinValueValidator(2), django.core.validators.MaxValueValidator(10)]),
        ),
        migrations.CreateModel(
            name='Report',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('usage', models.IntegerField(default=0)),
                ('store', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='reports', to='rewards.store')),
            ],
        ),
    ]
