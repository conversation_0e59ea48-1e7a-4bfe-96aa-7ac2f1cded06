# Generated by Django 3.2 on 2023-02-12 20:50

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('rewards', '0010_rename_store_reward_stores'),
    ]

    operations = [
        migrations.CreateModel(
            name='Address',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('line1', models.CharField(max_length=1024)),
                ('line2', models.CharField(blank=True, max_length=1024, null=True)),
                ('line3', models.CharField(blank=True, max_length=1024, null=True)),
                ('city', models.CharField(max_length=1024)),
                ('county', models.CharField(max_length=1024)),
                ('postcode', models.CharField(max_length=8)),
                ('latitude', models.FloatField(blank=True, null=True)),
                ('longitude', models.FloatField(blank=True, null=True)),
            ],
        ),
        migrations.Alter<PERSON>ield(
            model_name='reward',
            name='stores',
            field=models.ManyToManyField(related_name='rewards', to='rewards.Store'),
        ),
        migrations.AddField(
            model_name='company',
            name='address',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='rewards.address'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='store',
            name='address',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='rewards.address'),
            preserve_default=False,
        ),
    ]
