# Generated by Django 3.2 on 2022-08-26 14:01

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('rewards', '0004_auto_20220826_0925'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='reward',
            name='rewarded_by',
        ),
        migrations.RemoveField(
            model_name='reward',
            name='user',
        ),
        migrations.AddField(
            model_name='reward',
            name='owner',
            field=models.ForeignKey(default=False, on_delete=django.db.models.deletion.CASCADE, related_name='owned_rewards', to='accounts.user'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='reward',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
    ]
