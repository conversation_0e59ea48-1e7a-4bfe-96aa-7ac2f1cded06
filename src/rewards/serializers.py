from .models import Address, Company, Store, Reward, Point
from rest_framework import serializers, exceptions
from rest_framework.utils import html
from rest_framework.fields import empty
from django.contrib.auth import get_user_model

import re
from collections import OrderedDict
from django.utils.datastructures import MultiValueDict

User = get_user_model()

class AddressSerializer(serializers.ModelSerializer):
    class Meta: 
        model = Address
        fields = ['id', 'line1', 'line2', 'line3', 'city', 'county', 'postcode', 'latitude', 'longitude']


class StoreSerializer(serializers.ModelSerializer):
    address = AddressSerializer()
    # location_name = serializers.SerializerMethodField()
    location_logo = serializers.ImageField(required=False)

    class Meta:
        model = Store
        fields = ['id', 'name', 'company', 'address', 'location_logo', 'owner']
        read_only_fields = ['location_logo']

    def create(self, validated_data):
        address_data = validated_data.pop('address')
        address = Address.objects.create(**address_data)
        store = Store.objects.create(address=address, **validated_data)
        return store
    
    def update(self, instance, validated_data):
        address_data = validated_data.pop('address')
        address = instance.address
        saved_instance = super().update(instance, validated_data)
        address.city = address_data.get('city', address.city)
        address.county = address_data.get('county', address.county)
        address.line1 = address_data.get('line1', address.line1)
        address.line2 = address_data.get('line2', address.line2)
        address.line3 = address_data.get('line3', address.line3)
        address.postcode = address_data.get('postcode', address.postcode)
        address.save()
        return saved_instance
    

class RewardSerializer(serializers.ModelSerializer):
    class Meta:
        model = Reward
        fields = ['id', 'name', 'description', 'amount', 'company', 'owner', 'stores']
        read_only_fields = ['id', 'owner']

    def create(self, validated_data):
        user = self.context['request'].user
        stores = validated_data.pop('stores')
        reward = Reward.objects.create(owner=user, **validated_data)
        reward.stores.set(stores)
        return reward

    def validate_company(self, value):
        if self.context['request'].user != value.owner:
            raise exceptions.PermissionDenied()

        return value


class CompanySerializer(serializers.ModelSerializer):
    address = AddressSerializer(read_only=True)
    rewards = RewardSerializer(many=True, read_only=True, required=False)
    stores = StoreSerializer(many=True, read_only=True, required=False)

    class Meta:
        model = Company
        fields = ['id', 'name', 'address', 'logo', 'stores', 'rewards']

    def create(self, validated_data):
        user = self.context['request'].user
        address_data = validated_data.pop('address')
        address = Address.objects.create(**address_data)
        company = Company.objects.create(owner=user, address=address, **validated_data)
        return company
    
    def update(self, instance, validated_data):
        address_data = validated_data.pop('address')
        address = instance.address
        saved_instance = super().update(instance, validated_data)
        address.city = address_data.get('city', address.city)
        address.county = address_data.get('county', address.county)
        address.line1 = address_data.get('line1', address.line1)
        address.line2 = address_data.get('line2', address.line2)
        address.line3 = address_data.get('line3', address.line3)
        address.postcode = address_data.get('postcode', address.postcode)
        address.save()
        return saved_instance


class PointWriteSerializer(serializers.ModelSerializer):
    class Meta:
        model = Point
        fields = ['id', 'created_at', 'amount', 'reward', 'user', 'store']
        read_only_fields = ['store']


class PointReadSerializer(serializers.ModelSerializer):
    reward = RewardSerializer(read_only=True)
    store = StoreSerializer(read_only=True)

    class Meta:
        model = Point
        fields = ['id', 'created_at', 'amount', 'reward', 'user', 'store']


    def validate_reward(self, value):
        if self.context['request'].user != value.owner:
            raise exceptions.PermissionDenied()

        return value


class RewardPointsSummarySerializer(serializers.ModelSerializer):
    earned = serializers.IntegerField()
    last_collected_at = serializers.DateTimeField()
    company = CompanySerializer()

    class Meta:
        model = Reward
        fields = ['id', 'name', 'description', 'company', 'amount', 'earned', 'last_collected_at']
        read_only_fields = ['last_collected_at']