from django.db import models
from django.conf import settings
from django.core.validators import MaxValueValidator, MinValueValidator
from django.utils.html import mark_safe
import uuid, os
from datetime import datetime, timedelta


class Address(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    line1 = models.CharField(max_length=1024)
    line2 = models.CharField(max_length=1024, blank=True, null=True)
    line3 = models.CharField(max_length=1024, blank=True, null=True)
    city = models.CharField(max_length=1024)
    county = models.CharField(max_length=1024)
    postcode = models.CharField(max_length=8)
    latitude = models.FloatField(blank=True, null=True)
    longitude = models.FloatField(blank=True, null=True)

    def __str__(self):
        all_fields = [self.line1, self.line2, self.line3, self.city, self.county, self.postcode, self.latitude, self.longitude]
        valid_fields = [f for f in all_fields if f]
        return ', '.join(valid_fields)


def get_file_path(instance, filename):
    ext = filename.split('.')[-1]
    filename = "%s.%s" % (instance.id, ext)
    return os.path.join('company-logos', filename)


class Company(models.Model):
    class Meta:
        verbose_name_plural = 'Companies'

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='owned_companies')
    employees = models.ManyToManyField(settings.AUTH_USER_MODEL, related_name='companies')
    created_at = models.DateTimeField(auto_now_add=True, editable=False)
    updated_at = models.DateTimeField(auto_now=True, editable=False)
    address = models.OneToOneField(Address, on_delete=models.CASCADE)
    logo = models.ImageField(upload_to=get_file_path, null=True, blank=True)

    def __str__(self) -> str:
        return f'{self.name} ({self.id})'
    
    def image_tag(self):
        return mark_safe('<img src="/media/%s" width="50" />' % (self.logo))

    image_tag.short_description = 'Image'
    

class Store(models.Model):
    class Meta:
        ordering = ['name']

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='stores')
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='owned_stores')
    employees = models.ManyToManyField(settings.AUTH_USER_MODEL, related_name='stores')
    created_at = models.DateTimeField(auto_now_add=True, editable=False)
    updated_at = models.DateTimeField(auto_now=True, editable=False)
    address = models.OneToOneField(Address, on_delete=models.CASCADE)

    def __str__(self) -> str:
        return f'{self.name} ({self.id})'

    @property
    def location_logo(self):
        return self.company.logo


# Reward is the overall goal
class Reward(models.Model):
    class Meta:
        unique_together = (('company', 'name'),)

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=256)
    description = models.TextField(blank=True, null=True)
    amount = models.PositiveIntegerField(default=0, validators=[MinValueValidator(2), MaxValueValidator(10)])
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='rewards')
    stores = models.ManyToManyField(Store, related_name='rewards')
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='owned_rewards')
    created_at = models.DateTimeField(auto_now_add=True, editable=False)
    updated_at = models.DateTimeField(auto_now=True, editable=False)

    def __str__(self) -> str:
        return f'{self.name} ({self.id})'


# Point is the actual thing earned or redeemed (the transaction)
# Earning: +1 point
# Redeeming: -reward.amount points
class Point(models.Model):
    class Meta:
        ordering = ['created_at']

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(auto_now_add=True, editable=False)
    amount = models.IntegerField(default=1)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='points')
    reward = models.ForeignKey(Reward, on_delete=models.DO_NOTHING, related_name='points')
    store = models.ForeignKey(Store, on_delete=models.DO_NOTHING, related_name='points')

    def __str__(self) -> str:
        return f'{self.id}'


class Report(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(auto_now_add=True, editable=False)
    updated_at = models.DateTimeField(auto_now=True, editable=False)
    start_date = models.DateField()
    end_date = models.DateField()
    store = models.ForeignKey(Store, on_delete=models.DO_NOTHING, related_name='reports')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, related_name='reports')
    usage = models.IntegerField(default=0)