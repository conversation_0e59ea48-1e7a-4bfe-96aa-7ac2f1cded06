import json
from os import O_WRONLY
from urllib import response
from rest_framework import status
from rest_framework.test import APIClient
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from ..models import Company, Store, Reward, Point, Address
from ..serializers import CompanySerializer
from authentication.models import Token
import uuid

User = get_user_model()


class RewardsTestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create(email='<EMAIL>', password='test123', first_name='Test', last_name='Account')
        self.token = Token.objects.create(user=self.user)

        self.fake_user = User.objects.create(email='<EMAIL>', password='test123', first_name='Fake', last_name='Account')
        self.fake_token = Token.objects.create(user=self.fake_user)
        
        self.create_client()

    def create_client(self, user=None):
        if user == None:
            user = self.user

        self.client = APIClient()
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {user.auth_token.key}')


class ListCreateCompanyViewTest(RewardsTestCase):

    def setUp(self):
        super().setUp()

        self.valid_company_payload = {
            'name': 'Test Company 1',
            'address': {
                'line1': '123 Test Street',
                'city': 'Testington',
                'county': 'Testshire',
                'postcode': 'T3ST'
            }
        }

    # POST
    def test_create_valid_company(self):
        response = self.client.post(
            reverse('list_create_companies_view'),
            data=json.dumps(self.valid_company_payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_create_invalid_company(self):
        response = self.client.post(
            reverse('list_create_companies_view'),
            data=json.dumps({}),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    # GET
    def test_list_companies(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        Company.objects.create(name='Test Company', address=address, owner=self.user)
        response = self.client.get(
            reverse('list_create_companies_view')
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_list_companies_with_owner_filter(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        Company.objects.create(name='Test Company', address=address, owner=self.user)

        response = self.client.get(
            f'{reverse("list_create_companies_view")}?owner={str(self.user.id)}'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

        response = self.client.get(
            f'{reverse("list_create_companies_view")}?owner={str(self.fake_user.id)}'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)

    def test_list_companies_with_name_filter(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        Company.objects.create(name='Test Company', address=address, owner=self.user)
        
        response = self.client.get(
            f'{reverse("list_create_companies_view")}?name=test'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

        response = self.client.get(
            f'{reverse("list_create_companies_view")}?name=banana'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)


class GetCompanyViewTests(RewardsTestCase):

    def test_get_valid_company(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)

        response = self.client.get(
            reverse('get_update_company_view', args=[company.id])
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_get_invalid_company(self):
        response = self.client.get(
            reverse('get_update_company_view', args=[
                uuid.uuid4()])
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class ListCreateStoreViewTests(RewardsTestCase):

    def valid_store_payload(self, company_id, address):
        return {
            'name': 'Test Store 1',
            'company': company_id,
            'address': address
        }

    # POST
    def test_create_valid_store(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)
        
        response = self.client.post(
            reverse('list_company_stores_view', args=[company.id]),
            data=json.dumps(self.valid_store_payload(str(company.id), {'line1': address.line1, 'city': address.city, 'county': address.county, 'postcode': address.postcode})),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_create_invalid_store(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)
        
        response = self.client.post(
            reverse('list_company_stores_view', args=[company.id]),
            data=json.dumps({}),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_store_with_invalid_user(self):
        self.create_client(user=self.fake_user)
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)

        response = self.client.post(
            reverse('list_company_stores_view', args=[company.id]),
            data=json.dumps(self.valid_store_payload(str(company.id), {'line1': address.line1, 'city': address.city, 'county': address.county, 'postcode': address.postcode})),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_list_stores(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)

        store = Store.objects.create(name='Test Store', address=address, company=company, owner=self.user)
        response = self.client.get(
            reverse('list_company_stores_view', args=[company.id])
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_list_stores_with_company_filter(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)
        store = Store.objects.create(name='Test Store', address=address, company=company, owner=self.user)
        response = self.client.get(
            f'{reverse("list_stores_view")}?company={str(company.id)}'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

        response = self.client.get(
            f'{reverse("list_stores_view")}?company={str(uuid.uuid4())}'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)

    def test_list_stores_with_owner_filter(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)
        store = Store.objects.create(name='Test Store', address=address, company=company, owner=self.user)
        response = self.client.get(
            f'{reverse("list_stores_view")}?owner={str(self.user.id)}'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

        response = self.client.get(
            f'{reverse("list_stores_view")}?owner={str(self.fake_user.id)}'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)

    def test_list_stores_with_name_filter(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)
        store = Store.objects.create(name='Test Store', address=address, company=company, owner=self.user)
        response = self.client.get(
            f'{reverse("list_stores_view")}?name=test'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

        response = self.client.get(
            f'{reverse("list_stores_view")}?name=banana'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)


class GetStoreViewTests(RewardsTestCase):

    def test_get_valid_store(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)
        store = Store.objects.create(name='Test Store', address=address, company=company, owner=self.user)
        response = self.client.get(
            reverse('get_update_store_view', args=[store.id])
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_get_invalid_store(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)
        store = Store.objects.create(name='Test Store', address=address, company=company, owner=self.user)
        response = self.client.get(
            reverse('get_update_store_view', args=[uuid.uuid4()])
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class ListCreateRewardsViewTests(RewardsTestCase):

    def valid_reward_payload(self, company_id, store_id):
        return {
            'name': 'Test Reward',
            'description': 'This is the first reward created for a test',
            'amount': 10,
            'company': company_id,
            'stores': [store_id]
        }

    def test_create_valid_reward(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)
        store = Store.objects.create(name='Test Store', address=address, company=company, owner=self.user)

        response = self.client.post(
            reverse('list_create_rewards_view'),
            data=json.dumps(self.valid_reward_payload(str(company.id), str(store.id))),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_create_reward_with_invalid_data(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)
        store = Store.objects.create(name='Test Store', address=address, company=company, owner=self.user)

        response = self.client.post(
            reverse('list_create_rewards_view'),
            data=json.dumps(self.valid_reward_payload(str(uuid.uuid4()), str(uuid.uuid4()))),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_reward_with_invalid_user(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)
        store = Store.objects.create(name='Test Store', address=address, company=company, owner=self.user)
        
        self.create_client(user=self.fake_user)
        response = self.client.post(
            reverse('list_create_rewards_view'),
            data=json.dumps(self.valid_reward_payload(str(company.id), str(store.id))),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_list_rewards(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)
        store = Store.objects.create(name='Test Store', address=address, company=company, owner=self.user)
        reward = Reward.objects.create(name='Test Reward', amount=10, company=company, owner=self.user)
        response = self.client.get(
            reverse('list_create_rewards_view')
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_list_rewards_with_company_filter(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)
        store = Store.objects.create(name='Test Store', address=address, company=company, owner=self.user)
        reward = Reward.objects.create(name='Test Reward', amount=10, company=company, owner=self.user)
        response = self.client.get(
            f'{reverse("list_create_rewards_view")}?company={str(company.id)}'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

        response = self.client.get(
            f'{reverse("list_create_rewards_view")}?company={str(uuid.uuid4())}'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)

    def test_list_rewards_with_owner_filter(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)
        store = Store.objects.create(name='Test Store', address=address, company=company, owner=self.user)
        reward = Reward.objects.create(name='Test Reward', amount=10, company=company, owner=self.user)

        response = self.client.get(
            f'{reverse("list_create_rewards_view")}?owner={str(self.user.id)}'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

        response = self.client.get(
            f'{reverse("list_create_rewards_view")}?owner={str(self.fake_user.id)}'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)

    def test_list_rewards_with_name_filter(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)
        store = Store.objects.create(name='Test Store', address=address, company=company, owner=self.user)
        reward = Reward.objects.create(name='Test Reward', amount=10, company=company, owner=self.user)
        response = self.client.get(
            f'{reverse("list_create_rewards_view")}?name=test'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

        response = self.client.get(
            f'{reverse("list_create_rewards_view")}?name=banana'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)

    def test_list_rewards_with_amount_filter(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)
        store = Store.objects.create(name='Test Store', address=address, company=company, owner=self.user)
        reward = Reward.objects.create(name='Test Reward', amount=10, company=company, owner=self.user)
        response = self.client.get(
            f'{reverse("list_create_rewards_view")}?amount={reward.amount}'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

        response = self.client.get(
            f'{reverse("list_create_rewards_view")}?amount=4'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)


class GetUpdateRewardViewTest(RewardsTestCase):
    def test_get_valid_reward(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)
        store = Store.objects.create(name='Test Store', address=address, company=company, owner=self.user)
        reward = Reward.objects.create(name='Test Reward', amount=10, company=company, owner=self.user)

        response = self.client.get(
            reverse('get_reward_view', args=[reward.id])
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_get_invalid_reward(self):
        response = self.client.get(
            reverse('get_reward_view', args=[uuid.uuid4()])
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class ListCreatePointsViewTests(RewardsTestCase):
    def valid_point_payload(self, user_id, reward_id):
        return [{
            'amount': 1,
            'user': user_id,
            'reward': reward_id
        }]

    def test_create_point_with_valid_data(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)
        store = Store.objects.create(name='Test Store', address=address, company=company, owner=self.user)
        reward = Reward.objects.create(name='Test Reward', amount=10, company=company, owner=self.user)
        response = self.client.post(
            reverse('list_create_store_points_view', args=[str(store.id)]),
            json.dumps(self.valid_point_payload(str(self.user.id), str(reward.id))),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_create_point_with_invalid_data(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)
        store = Store.objects.create(name='Test Store', address=address, company=company, owner=self.user)
        reward = Reward.objects.create(name='Test Reward', amount=10, company=company, owner=self.user)
        response = self.client.post(
            reverse('list_create_store_points_view', args=[str(store.id)]),
            json.dumps(self.valid_point_payload(str(self.user.id), str(uuid.uuid4()))),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_list_points_for_user(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)
        store = Store.objects.create(name='Test Store', address=address, company=company, owner=self.user)
        reward = Reward.objects.create(name='Test Reward', amount=10, company=company, owner=self.user)
        point = Point.objects.create(amount=1, reward=reward, store=store, user=self.user)
        
        response = self.client.get(
            reverse('list_create_store_points_view', args=[str(store.id)]),
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

        self.create_client(self.fake_user)

        response = self.client.get(
            reverse('list_create_store_points_view', args=[str(store.id)]),
        )
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_list_points_for_user_with_reward_filter(self):
        address = Address.objects.create(line1='123 Test Street', city='Testington', county='Testingshire', postcode='T3ST')
        company = Company.objects.create(name='Test Company', address=address, owner=self.user)
        store = Store.objects.create(name='Test Store', address=address, company=company, owner=self.user)
        reward = Reward.objects.create(name='Test Reward', amount=10, company=company, owner=self.user)
        Point.objects.create(amount=1, reward=reward, store=store, user=self.user)
        Point.objects.create(amount=1, reward=reward, store=store, user=self.user)
        
        response = self.client.get(
            f'{reverse("list_create_store_points_view", args=[str(store.id)])}?reward={str(reward.id)}'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)

        self.create_client(self.fake_user)

        response = self.client.get(
            f'{reverse("list_create_store_points_view", args=[str(store.id)])}?reward={str(reward.id)}'
        )
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

