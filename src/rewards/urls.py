from django.urls import path
from . import views

appname = 'rewards'

urlpatterns = [
    path('companies', views.ListCreateCompanyView.as_view(), name='list_create_companies_view'),
    path('companies/<uuid:pk>', views.GetUpdateCompanyView.as_view(), name='get_update_company_view'),
    path('companies/<uuid:pk>/rewards', views.ListCompanyRewardsView.as_view(), name='list_company_rewards_view'),
    path('companies/<uuid:pk>/stores', views.ListCreateCompanyStoresView.as_view(), name='list_company_stores_view'),
    path('stores', views.ListStoreView.as_view(), name='list_stores_view'),
    path('stores/<uuid:pk>', views.GetUpdateStoreView.as_view(), name='get_update_store_view'),
    path('stores/<uuid:pk>/rewards', views.ListStoreRewardsView.as_view(), name='get_store_rewards_view'),
    path('rewards', views.ListCreateRewardsView.as_view(), name='list_create_rewards_view'),
    path('rewards/<uuid:pk>', views.GetUpdateRewardView.as_view(), name='get_reward_view'),
    path('rewards/<uuid:pk>/stores', views.ListRewardStoresView.as_view(), name='list_reward_stores_view'),
    path('rewards/summary', views.RewardPointsSummaryView.as_view(), name='reward_points_summary_view'),

    ## The endpoint that allows a user to see all of their points they've earned (and redeemed)
    ## - filterable
    path('users/<uuid:pk>/points', views.ListUserPointsView.as_view(), name='list_user_points_view'),

    ## The endpoint for owners to create a point, and list points, for a given store
    path('stores/<uuid:pk>/points', views.ListCreateStorePointsView.as_view(), name='list_create_store_points_view'),

    path('users/<uuid:pk>/companies', views.ListUserCompaniesView.as_view(), name='list_user_companies_view'),

    path('stores/<uuid:pk>/redeemables', views.ListStoreRedeemablesView.as_view(), name='list_store_redeemables_view'),
]