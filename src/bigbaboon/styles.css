/* General Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

section {
    padding: 80px 0;
}

h1, h2, h3 {
    margin-bottom: 15px;
    line-height: 1.2;
}

p {
    margin-bottom: 10px;
}

.btn {
    display: inline-block;
    background: #FF7F00; /* Orange accent color */
    color: #fff;
    padding: 12px 25px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn:hover {
    background: #E67300; /* Darker orange on hover */
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    font-size: 2.5rem;
    position: relative;
    display: inline-block;
    margin-bottom: 20px;
}

.section-header h2::after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -10px;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: #FF7F00; /* Orange accent color */
}

/* Navbar */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    padding: 10px 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo img {
    height: 60px;
    width: auto;
}

.nav-menu {
    display: flex;
    list-style: none;
}

.nav-menu li {
    margin-left: 30px;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover, .nav-menu a.active {
    color: #FF7F00; /* Orange accent color */
}

.menu-toggle {
    display: none;
    cursor: pointer;
    font-size: 1.5rem;
}

/* Hero Section */
.hero {
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('baboon.svg') no-repeat center center/contain;
    height: 100vh;
    display: flex;
    align-items: center;
    text-align: center;
    color: #fff;
    margin-top: 0;
    background-color: #222;
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 20px;
}

.hero-content p {
    font-size: 1.5rem;
    margin-bottom: 30px;
}

/* Services Section */
.services {
    background: #f9f9f9;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.service-card {
    background: #fff;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
}

.service-card i {
    font-size: 2.5rem;
    color: #FF7F00; /* Orange accent color */
    margin-bottom: 20px;
}

/* Product Section */
.product-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 40px;
}

.product-text, .product-image {
    flex: 1 1 45%;
}

.product-image img {
    width: 100%;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.product-text ul {
    list-style: none;
    margin: 20px 0;
}

.product-text li {
    margin-bottom: 10px;
    display: flex;
    align-items: flex-start;
}

.product-text li i {
    color: #FF7F00; /* Orange accent color */
    margin-right: 10px;
}

/* Contact Section */
.contact {
    background: #f9f9f9;
}

.contact-content {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
}

.contact-info, .contact-form {
    flex: 1 1 45%;
}

.info-item {
    margin-bottom: 30px;
}

.info-item i {
    font-size: 1.5rem;
    color: #FF7F00; /* Orange accent color */
    margin-bottom: 10px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group input, .form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-family: 'Poppins', sans-serif;
}

.form-group textarea {
    height: 150px;
    resize: vertical;
}

/* Footer */
.footer {
    background: #222;
    color: #fff;
    padding: 60px 0 20px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 40px;
}

.footer-img {
    height: 60px;
    width: auto;
    filter: brightness(0) invert(1); /* Makes the logo white */
    margin-bottom: 15px;
}

.social-links a {
    display: inline-block;
    color: #fff;
    margin-left: 15px;
    font-size: 1.5rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: #FF7F00; /* Orange accent color */
}

.copyright {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .menu-toggle {
        display: block;
    }

    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        background: #fff;
        flex-direction: column;
        align-items: center;
        padding: 20px 0;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
        transition: left 0.3s ease;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 15px 0;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-content p {
        font-size: 1.2rem;
    }

    .product-text, .product-image,
    .contact-info, .contact-form {
        flex: 1 1 100%;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .social-links {
        margin-top: 20px;
    }

    .social-links a {
        margin: 0 10px;
    }
}